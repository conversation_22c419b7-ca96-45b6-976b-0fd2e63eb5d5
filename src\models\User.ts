import mongoose, {
  type Document,
  Schema,
  Types,
  type Model,
  type CallbackWithoutResultAndOptionalError
} from 'mongoose'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import { env } from '../utils/env'
import { ConfigurationError, DatabaseError } from '../common/errors'
import { validateEmail } from '../utils/helpers'

// Interfac declarations
export interface IUser extends Document {
  firstName: string
  lastName: string
  email: string
  password: Types.ObjectId
  roles: Types.Array<Types.ObjectId | string>
  generateAuthToken: () => string
  generateFakeAuthToken: () => string
}

export interface IUserData extends Document {
  firstName: string
  lastName: string
  email: string
  password: string
  roles: Types.Array<Types.ObjectId>
}

export interface IUserRole extends Document {
  name: string
  permissions: string[]
}

export interface IPassword extends Document {
  hash: string
  user: Types.ObjectId
  checkPassword: (password: string) => Promise<boolean>
}

// -----------------------------------------------

// Schema declarations
const roleSchema: Schema = new Schema<IUserRole>(
  {
    name: {
      type: String,
      unique: true
    },
    permissions: [String]
  },
  {
    timestamps: true,
    versionKey: false
  }
)

const userSchema: Schema = new Schema<IUser>(
  {
    firstName: {
      type: String,
      required: true,
      unique: false,
      trim: true
    },
    lastName: {
      type: String,
      required: false,
      unique: false,
      trim: true
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      match:
        /^(([^<>()[\].,;:\s@"]+(\.[^<>()[\].,;:\s@"]+)*)|(".+"))@(([^<>()[\].,;:\s@"]+\.)+[^<>()[\].,;:\s@"]{2,})$/,
      unique: true,
      trim: true
    },
    password: {
      type: Schema.Types.ObjectId,
      ref: 'Password',
      required: true
    },
    roles: [{ type: Types.ObjectId, ref: 'Role' }]
  },
  {
    timestamps: true,
    versionKey: false
  }
)

const passwordSchema: Schema = new Schema<IPassword>({
  hash: {
    type: String,
    required: true,
    trim: true
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    unique: true
  }
})
passwordSchema.pre<IPassword>('save', async function (next) {
  if (!this.isModified('hash')) {
    next()
    return
  }

  try {
    const salt = await bcrypt.genSalt(10)
    this.hash = await bcrypt.hash(this.hash, salt)

    next()
  } catch (error: unknown) {
    // Transform bcrypt errors to database errors
    const dbError = new DatabaseError('Failed to hash password', 500, {
      cause: error
    })
    next(dbError)
  }
})
passwordSchema.methods.checkPassword = async function (
  password: string
): Promise<boolean> {
  return await bcrypt.compare(password, this.hash)
}

userSchema.path('email').validate(validateEmail, 'Email is invalid')
userSchema.methods.generateAuthToken = function () {
  const secret = env('JWT_SECRET_KEY')
  if (!secret) {
    throw new ConfigurationError('JWT_SECRET_KEY is not set')
  }

  const token = jwt.sign({ _id: this._id }, secret, {
    expiresIn: '1h'
  })

  return token
}

/**
 * TODO: Plan for added security
 * Deceive the client that a registration or a login is successful
 */
userSchema.methods.generateFakeAuthToken = function () {
  const token = jwt.sign({ _id: this._id }, 'dummy', {
    expiresIn: '1h'
  })

  return token
}
userSchema.methods.validatePayload = function (payload: object) {
  const data = {}

  return data
}
userSchema.plugin((schema: Schema) => {
  schema.post(
    'save',
    (err: any, doc: any, next: CallbackWithoutResultAndOptionalError) => {
      // if (error.name === 'ValidationError') {

      // }

      next(err)
    }
  )
})

const User = mongoose.models.User || mongoose.model<IUser>('User', userSchema)

export const Role =
  (mongoose.models.Role as Model<IUserRole>) ||
  mongoose.model<IUserRole>('Role', roleSchema)
export const Password =
  (mongoose.models.Password as Model<IPassword>) ||
  mongoose.model<IPassword>('Password', passwordSchema)
export default User
