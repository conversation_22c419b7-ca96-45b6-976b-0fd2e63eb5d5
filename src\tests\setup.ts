// Test setup file for Jest
// This file runs before all tests

// Mock console methods to reduce noise in test output
global.console = {
  ...console,
  // Uncomment to ignore specific console methods during tests
  // log: jest.fn(),
  // debug: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
}

// Set test environment variables
process.env.NODE_ENV = 'test'
process.env.JWT_SECRET_KEY = 'test-secret-key'
process.env.MONGO_URI = 'mongodb://localhost:27017/test'
process.env.APP_DEBUG = 'false'

// Global test utilities
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeAppError(): R
      toHaveStatusCode(statusCode: number): R
    }
  }
}

// Custom Jest matchers for error testing
expect.extend({
  toBeAppError(received) {
    const pass = received && 
                 typeof received === 'object' && 
                 'statusCode' in received && 
                 'message' in received &&
                 received instanceof Error

    if (pass) {
      return {
        message: () => `expected ${received} not to be an AppError`,
        pass: true,
      }
    } else {
      return {
        message: () => `expected ${received} to be an AppError`,
        pass: false,
      }
    }
  },

  toHaveStatusCode(received, statusCode) {
    const pass = received && 
                 typeof received === 'object' && 
                 'statusCode' in received && 
                 received.statusCode === statusCode

    if (pass) {
      return {
        message: () => `expected ${received} not to have status code ${statusCode}`,
        pass: true,
      }
    } else {
      return {
        message: () => `expected ${received} to have status code ${statusCode}, but got ${received?.statusCode}`,
        pass: false,
      }
    }
  },
})

// Mock external dependencies that might not be available in test environment
jest.mock('mongoose', () => ({
  connect: jest.fn(),
  connection: {
    readyState: 0,
    on: jest.fn(),
  },
  STATES: {
    connected: 1,
  },
  set: jest.fn(),
}))

// Export test utilities
export const createMockError = (message: string, code?: number) => {
  const error = new Error(message)
  if (code) {
    (error as any).code = code
  }
  return error
}

export const createMockMongoError = (code: number, message: string) => ({
  code,
  message,
  name: 'MongoError'
})

export const createMockJWTError = (name: string, message: string) => {
  const error = new Error(message)
  error.name = name
  return error
}
