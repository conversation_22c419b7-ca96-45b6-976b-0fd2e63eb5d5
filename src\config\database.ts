import Mongoose from 'mongoose'
import { env } from '../utils/env'
import { DatabaseConnectionError } from '../common/errors'

const isDebug = env('APP_DEBUG', 'true') === 'true'

export const initializeDatabase = async (): Promise<void> => {
  Mongoose.set('debug', isDebug)

  try {
    // Prevent reconnecting to the database
    if (Mongoose.connection.readyState === Mongoose.STATES.connected) {
      console.log('We are already connected.')
      return
    }

    Mongoose.connection.on('connected', () => {
      console.log('Connected to the database.')
    })

    Mongoose.connection.on('error', (err: Error) => {
      console.log('Having issues connecting to the database:', err.message)
    })

    await Mongoose.connect(env('MONGO_URI'), {})
  } catch (error: unknown) {
    // Log the original error for debugging
    if (error instanceof Error) {
      console.log('Error connecting to MongoDB:', error.message)
    }

    // Throw a properly typed database connection error
    throw new DatabaseConnectionError('Failed to connect to MongoDB', {
      cause: error
    })
  }
}
