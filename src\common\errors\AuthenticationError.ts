import AppError, { type ErrorContext } from './AppError'

/**
 * Base class for all authentication-related errors
 */
export class AuthenticationError extends AppError {
  constructor(
    message: string = 'Authentication failed',
    statusCode: number = 401,
    context?: ErrorContext
  ) {
    super(message, statusCode, context)
  }
}

/**
 * Error thrown when credentials are invalid
 */
export class InvalidCredentialsError extends AuthenticationError {
  constructor(
    message: string = 'Invalid credentials provided',
    context?: ErrorContext
  ) {
    super(message, 401, context)
  }
}

/**
 * Error thrown when JWT token is expired
 */
export class TokenExpiredError extends AuthenticationError {
  constructor(
    message: string = 'Authentication token has expired',
    context?: ErrorContext
  ) {
    super(message, 401, context)
  }
}

/**
 * Error thrown when JWT token is invalid or malformed
 */
export class TokenInvalidError extends AuthenticationError {
  constructor(
    message: string = 'Invalid authentication token',
    context?: ErrorContext
  ) {
    super(message, 401, context)
  }
}

/**
 * Error thrown when no authentication token is provided
 */
export class TokenMissingError extends AuthenticationError {
  constructor(
    message: string = 'Authentication token is required',
    context?: ErrorContext
  ) {
    super(message, 401, context)
  }
}

/**
 * Error thrown when user account is locked or disabled
 */
export class AccountDisabledError extends AuthenticationError {
  constructor(
    message: string = 'User account is disabled',
    context?: ErrorContext
  ) {
    super(message, 401, context)
  }
}

export default AuthenticationError
