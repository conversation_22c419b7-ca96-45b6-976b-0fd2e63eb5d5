import AppError, { type ErrorContext } from './AppError'

/**
 * Base class for all authorization-related errors
 */
export class AuthorizationError extends AppError {
  constructor(
    message: string = 'Access denied',
    statusCode: number = 403,
    context?: ErrorContext
  ) {
    super(message, statusCode, context)
  }
}

/**
 * Error thrown when user lacks required permissions
 */
export class PermissionDeniedError extends AuthorizationError {
  constructor(
    message: string = 'Permission denied',
    context?: ErrorContext
  ) {
    super(message, 403, context)
  }
}

/**
 * Error thrown when user lacks required role
 */
export class RoleRequiredError extends AuthorizationError {
  constructor(
    message: string = 'Required role not found',
    context?: ErrorContext
  ) {
    super(message, 403, context)
  }
}

/**
 * Error thrown when resource access is forbidden
 */
export class ResourceAccessDeniedError extends AuthorizationError {
  constructor(
    message: string = 'Access to this resource is denied',
    context?: ErrorContext
  ) {
    super(message, 403, context)
  }
}

/**
 * Error thrown when operation is forbidden
 */
export class OperationForbiddenError extends AuthorizationError {
  constructor(
    message: string = 'Operation is forbidden',
    context?: ErrorContext
  ) {
    super(message, 403, context)
  }
}

export default AuthorizationError
