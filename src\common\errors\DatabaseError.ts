import AppError, { type ErrorContext } from './AppError'

/**
 * Base class for all database-related errors
 */
export class DatabaseError extends AppError {
  constructor(
    message: string = 'Database operation failed',
    statusCode: number = 500,
    context?: ErrorContext
  ) {
    super(message, statusCode, context)
  }
}

/**
 * Error thrown when database connection fails
 */
export class DatabaseConnectionError extends DatabaseError {
  constructor(
    message: string = 'Failed to connect to database',
    context?: ErrorContext
  ) {
    super(message, 503, context)
  }
}

/**
 * Error thrown when a database query fails
 */
export class DatabaseQueryError extends DatabaseError {
  constructor(
    message: string = 'Database query failed',
    context?: ErrorContext
  ) {
    super(message, 500, context)
  }
}

/**
 * Error thrown when database validation fails
 */
export class DatabaseValidationError extends DatabaseError {
  constructor(
    message: string = 'Database validation failed',
    context?: ErrorContext
  ) {
    super(message, 400, context)
  }
}

/**
 * Error thrown when a duplicate key constraint is violated
 */
export class DatabaseDuplicateError extends DatabaseError {
  constructor(
    message: string = 'Duplicate entry found',
    context?: ErrorContext
  ) {
    super(message, 409, context)
  }
}

/**
 * Error thrown when a database transaction fails
 */
export class DatabaseTransactionError extends DatabaseError {
  constructor(
    message: string = 'Database transaction failed',
    context?: ErrorContext
  ) {
    super(message, 500, context)
  }
}

export default DatabaseError
