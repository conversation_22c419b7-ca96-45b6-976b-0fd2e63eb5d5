import 'dotenv/config'
import './config'

import express, { type Application, type Request, type Response } from 'express'
import { appMiddlewares } from './middleware/appMiddlewares'
import registerRoutes from './utils/registerRoutes'
import { initializeDatabase } from './config/database'
import errorMiddleware from './middleware/errorMiddleware'
import { SetupError } from './common/errors'
import { getErrorMessage } from './common/errors/errorUtils'

interface IApp {
  app: Application
  port: number | string | undefined
}

async function createApp(): Promise<IApp> {
  // Connect to the database first
  await initializeDatabase()

  const port = process.env.PORT
  const app: Application = express()

  app.use(express.urlencoded({ limit: '50mb', extended: true }))
  app.use(express.json({ limit: '50mb' }))

  appMiddlewares(app)
  registerRoutes(app)

  app.use('*', (req: Request, res: Response) => {
    res.notFound()
  })

  /**
   * This is placed at the last middleware declaration
   */
  app.use(errorMiddleware)

  return { app, port }
}

try {
  void createApp()
    .then(({ app, port }) => {
      app.listen(port, () => {
        console.log(`Server running in http://localhost:${port}`)
      })
    })
    .catch((error: unknown) => {
      // Handle application startup errors with proper typing
      const setupError = new SetupError('Failed to start application', {
        cause: error
      })
      console.error('Application startup failed:', getErrorMessage(setupError))
      console.error('Error details:', setupError.context?.cause)
      process.exit(1)
    })
} catch (error: unknown) {
  // Handle synchronous errors during app creation
  const setupError = new SetupError('Failed to initialize application', {
    cause: error
  })
  console.error(
    'Application initialization failed:',
    getErrorMessage(setupError)
  )
  console.error('Error details:', setupError.context?.cause)
  process.exit(1)
}
