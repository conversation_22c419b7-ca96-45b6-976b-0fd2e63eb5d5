import {
  isAppError,
  isDatabaseError,
  isAuthenticationError,
  isValidationError,
  transformError,
  transformMongoError,
  getErrorMessage,
  getErrorStatusCode
} from '../../common/errors/errorUtils'
import {
  AppError,
  DatabaseError,
  DatabaseConnectionError,
  DatabaseQueryError,
  DatabaseValidationError,
  DatabaseDuplicateError,
  AuthenticationError,
  TokenInvalidError,
  TokenExpiredError,
  ValidationError,
  ResourceNotFoundError
} from '../../common/errors'

describe('Error Utils', () => {
  describe('Type Guards', () => {
    it('should correctly identify AppError instances', () => {
      const appError = new AppError('Test error')
      const regularError = new Error('Regular error')
      const stringError = 'String error'

      expect(isAppError(appError)).toBe(true)
      expect(isAppError(new DatabaseError())).toBe(true)
      expect(isAppError(new AuthenticationError())).toBe(true)
      expect(isAppError(regularError)).toBe(false)
      expect(isAppError(stringError)).toBe(false)
      expect(isAppError(null)).toBe(false)
      expect(isAppError(undefined)).toBe(false)
    })

    it('should correctly identify DatabaseError instances', () => {
      const dbError = new DatabaseError()
      const dbConnectionError = new DatabaseConnectionError()
      const appError = new AppError()
      const authError = new AuthenticationError()

      expect(isDatabaseError(dbError)).toBe(true)
      expect(isDatabaseError(dbConnectionError)).toBe(true)
      expect(isDatabaseError(appError)).toBe(false)
      expect(isDatabaseError(authError)).toBe(false)
    })

    it('should correctly identify AuthenticationError instances', () => {
      const authError = new AuthenticationError()
      const tokenError = new TokenInvalidError()
      const dbError = new DatabaseError()
      const appError = new AppError()

      expect(isAuthenticationError(authError)).toBe(true)
      expect(isAuthenticationError(tokenError)).toBe(true)
      expect(isAuthenticationError(dbError)).toBe(false)
      expect(isAuthenticationError(appError)).toBe(false)
    })

    it('should correctly identify ValidationError instances', () => {
      const validationError = new ValidationError()
      const authError = new AuthenticationError()
      const appError = new AppError()

      expect(isValidationError(validationError)).toBe(true)
      expect(isValidationError(authError)).toBe(false)
      expect(isValidationError(appError)).toBe(false)
    })
  })

  describe('transformError', () => {
    it('should return AppError instances as-is', () => {
      const originalError = new DatabaseError('Original database error')
      const result = transformError(originalError)

      expect(result).toBe(originalError)
    })

    it('should transform MongoDB errors', () => {
      const mongoError = new Error('Connection failed')
      mongoError.name = 'MongoNetworkError'

      const result = transformError(mongoError)

      expect(result).toBeInstanceOf(DatabaseConnectionError)
      expect(result.message).toBe('Connection failed')
      expect(result.context?.cause).toBe(mongoError)
    })

    it('should transform JWT errors', () => {
      const jwtError = new Error('Invalid token')
      jwtError.name = 'JsonWebTokenError'

      const result = transformError(jwtError)

      expect(result).toBeInstanceOf(TokenInvalidError)
      expect(result.message).toBe('Invalid token')
      expect(result.context?.cause).toBe(jwtError)
    })

    it('should transform TokenExpiredError', () => {
      const expiredError = new Error('Token expired')
      expiredError.name = 'TokenExpiredError'

      const result = transformError(expiredError)

      expect(result).toBeInstanceOf(TokenExpiredError)
      expect(result.message).toBe('Token expired')
    })

    it('should transform generic Error objects', () => {
      const genericError = new Error('Generic error message')

      const result = transformError(genericError)

      expect(result).toBeInstanceOf(AppError)
      expect(result.message).toBe('Generic error message')
      expect(result.statusCode).toBe(500)
      expect(result.context?.cause).toBe(genericError)
    })

    it('should transform string errors', () => {
      const stringError = 'String error message'

      const result = transformError(stringError)

      expect(result).toBeInstanceOf(AppError)
      expect(result.message).toBe('String error message')
    })

    it('should handle unknown errors with default message', () => {
      const unknownError = { someProperty: 'value' }

      const result = transformError(unknownError, 'Default message')

      expect(result).toBeInstanceOf(AppError)
      expect(result.message).toBe('Default message')
      expect(result.context?.cause).toBe(unknownError)
    })
  })

  describe('transformMongoError', () => {
    it('should transform duplicate key errors', () => {
      const mongoError = {
        code: 11000,
        message:
          'E11000 duplicate key error collection: test.users index: email_1 dup key: { email: "<EMAIL>" }'
      }

      const result = transformMongoError(mongoError)

      expect(result).toBeInstanceOf(DatabaseDuplicateError)
      expect(result.message).toContain('already exists')
    })

    it('should transform cast errors (invalid ObjectId)', () => {
      const castError = {
        name: 'CastError',
        kind: 'ObjectId',
        path: 'userId',
        value: 'invalid-id'
      }

      const result = transformMongoError(castError)

      expect(result).toBeInstanceOf(ResourceNotFoundError)
      expect(result.context?.details).toEqual({
        resource: 'userId',
        query: 'invalid-id'
      })
    })

    it('should transform validation errors', () => {
      const validationError = {
        name: 'ValidationError',
        errors: {
          email: { path: 'email', kind: 'required' },
          name: { path: 'name', kind: 'regexp' }
        }
      }

      const result = transformMongoError(validationError)

      expect(result).toBeInstanceOf(DatabaseValidationError)
      expect(result.message).toContain("The field 'email' is required")
      expect(result.message).toContain("The field 'name' is invalid")
    })

    it('should fallback to transformError for unknown mongo errors', () => {
      const unknownError = { code: 999, message: 'Unknown mongo error' }

      const result = transformMongoError(unknownError)

      expect(result).toBeInstanceOf(AppError)
    })
  })

  describe('getErrorMessage', () => {
    it('should extract message from AppError', () => {
      const error = new AppError('Custom error message')

      expect(getErrorMessage(error)).toBe('Custom error message')
    })

    it('should extract message from regular Error', () => {
      const error = new Error('Regular error message')

      expect(getErrorMessage(error)).toBe('Regular error message')
    })

    it('should return string errors as-is', () => {
      const error = 'String error'

      expect(getErrorMessage(error)).toBe('String error')
    })

    it('should return default message for unknown errors', () => {
      const error = { someProperty: 'value' }

      expect(getErrorMessage(error)).toBe('An unknown error occurred')
    })
  })

  describe('getErrorStatusCode', () => {
    it('should extract status code from AppError', () => {
      const error = new AppError('Test error', 422)

      expect(getErrorStatusCode(error)).toBe(422)
    })

    it('should return 500 for non-AppError instances', () => {
      const error = new Error('Regular error')

      expect(getErrorStatusCode(error)).toBe(500)
    })

    it('should return 500 for unknown errors', () => {
      const error = 'String error'

      expect(getErrorStatusCode(error)).toBe(500)
    })
  })
})
