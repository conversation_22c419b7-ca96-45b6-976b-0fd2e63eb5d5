import { Request, Response, NextFunction } from 'express'
import errorMiddleware from '../../middleware/errorMiddleware'
import {
  AppError,
  DatabaseConnectionError,
  DatabaseDuplicateError,
  DatabaseValidationError,
  DatabaseError,
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  ResourceError,
  ConfigurationError
} from '../../common/errors'

// Mock Express Response
const createMockResponse = () => {
  const res = {
    error: jest.fn(),
    response: jest.fn(),
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis()
  }
  return res as unknown as Response
}

const createMockRequest = () => ({} as Request)
const createMockNext = () => jest.fn() as NextFunction

describe('Error Middleware', () => {
  let req: Request
  let res: Response
  let next: NextFunction

  beforeEach(() => {
    req = createMockRequest()
    res = createMockResponse()
    next = createMockNext()
    jest.clearAllMocks()
  })

  describe('Database Errors', () => {
    it('should handle DatabaseConnectionError', () => {
      const error = new DatabaseConnectionError('Connection failed')
      
      errorMiddleware(error, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith(
        'Database connection failed. Please try again later.',
        503
      )
    })

    it('should handle DatabaseDuplicateError', () => {
      const error = new DatabaseDuplicateError('Email already exists')
      
      errorMiddleware(error, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith('Email already exists', 409)
    })

    it('should handle DatabaseValidationError', () => {
      const error = new DatabaseValidationError('Validation failed')
      
      errorMiddleware(error, req, res, next)
      
      expect(res.response).toHaveBeenCalledWith(
        { errors: { message: ['Validation failed'] } },
        400
      )
    })

    it('should handle generic DatabaseError', () => {
      const error = new DatabaseError('Generic database error')
      
      errorMiddleware(error, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith(
        'Database operation failed. Please try again later.',
        500
      )
    })
  })

  describe('Authentication Errors', () => {
    it('should handle AuthenticationError', () => {
      const error = new AuthenticationError('Invalid token', 401)
      
      errorMiddleware(error, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith('Invalid token', 401)
    })
  })

  describe('Authorization Errors', () => {
    it('should handle AuthorizationError', () => {
      const error = new AuthorizationError('Access denied', 403)
      
      errorMiddleware(error, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith('Access denied', 403)
    })
  })

  describe('Validation Errors', () => {
    it('should handle ValidationError', () => {
      const error = new ValidationError('Input validation failed', 400)
      
      errorMiddleware(error, req, res, next)
      
      expect(res.response).toHaveBeenCalledWith(
        { errors: { message: ['Input validation failed'] } },
        400
      )
    })
  })

  describe('Resource Errors', () => {
    it('should handle ResourceError', () => {
      const error = new ResourceError('Resource not found', 404)
      
      errorMiddleware(error, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith('Resource not found', 404)
    })
  })

  describe('Configuration Errors', () => {
    it('should handle ConfigurationError', () => {
      const error = new ConfigurationError('Config error', 500)
      
      errorMiddleware(error, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith(
        'Server configuration error. Please contact support.',
        500
      )
    })
  })

  describe('Unknown Errors', () => {
    it('should transform unknown errors to AppError', () => {
      const unknownError = new Error('Unknown error')
      
      errorMiddleware(unknownError, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith('Unknown error', 500)
    })

    it('should handle MongoDB duplicate key error', () => {
      const mongoError = {
        code: 11000,
        message: 'E11000 duplicate key error collection: test.users index: email_1 dup key: { email: "<EMAIL>" }'
      }
      
      errorMiddleware(mongoError, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith(
        expect.stringContaining('already exists'),
        409
      )
    })

    it('should handle string errors', () => {
      const stringError = 'Something went wrong'
      
      errorMiddleware(stringError, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith('Something went wrong', 500)
    })

    it('should handle null/undefined errors', () => {
      errorMiddleware(null, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith('An unknown error occurred', 500)
    })
  })

  describe('Default Error Handling', () => {
    it('should handle generic AppError', () => {
      const error = new AppError('Generic app error', 422)
      
      errorMiddleware(error, req, res, next)
      
      expect(res.error).toHaveBeenCalledWith('Generic app error', 422)
    })
  })
})
