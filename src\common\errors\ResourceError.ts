import AppError, { type ErrorContext } from './AppError'

/**
 * Base class for all resource-related errors
 */
export class ResourceError extends AppError {
  constructor(
    message: string = 'Resource operation failed',
    statusCode: number = 404,
    context?: ErrorContext
  ) {
    super(message, statusCode, context)
  }
}

/**
 * Error thrown when a resource is not found
 */
export class ResourceNotFoundError extends ResourceError {
  constructor(resource?: string, query?: any, context?: ErrorContext) {
    const message = resource
      ? `The resource ${resource} was not found`
      : 'Resource not found'

    const errorContext: ErrorContext = {
      ...context,
      details: { resource, query, ...context?.details }
    }

    super(message, 404, errorContext)
  }
}

/**
 * Error thrown when a resource already exists (conflict)
 */
export class ResourceConflictError extends ResourceError {
  constructor(resource?: string, context?: ErrorContext) {
    const message = resource
      ? `The resource ${resource} already exists`
      : 'Resource already exists'

    super(message, 409, context)
  }
}

/**
 * Error thrown when a resource is in an invalid state
 */
export class ResourceStateError extends ResourceError {
  constructor(
    message: string = 'Resource is in invalid state',
    context?: ErrorContext
  ) {
    super(message, 422, context)
  }
}

// Keep the original ResourceError as ResourceNotFoundError for backward compatibility
export default ResourceNotFoundError
