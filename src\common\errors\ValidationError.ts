import AppError, { type ErrorContext } from './AppError'

/**
 * Base class for all validation-related errors
 */
export class ValidationError extends AppError {
  constructor(
    message: string = 'Validation failed',
    statusCode: number = 400,
    context?: ErrorContext
  ) {
    super(message, statusCode, context)
  }
}

/**
 * Error thrown when input validation fails
 */
export class InputValidationError extends ValidationError {
  constructor(
    message: string = 'Input validation failed',
    context?: ErrorContext
  ) {
    super(message, 400, context)
  }
}

/**
 * Error thrown when schema validation fails
 */
export class SchemaValidationError extends ValidationError {
  constructor(
    message: string = 'Schema validation failed',
    context?: ErrorContext
  ) {
    super(message, 400, context)
  }
}

/**
 * Error thrown when required field is missing
 */
export class RequiredFieldError extends ValidationError {
  constructor(
    fieldName: string,
    context?: ErrorContext
  ) {
    super(`The field '${fieldName}' is required`, 400, context)
  }
}

/**
 * Error thrown when field format is invalid
 */
export class InvalidFormatError extends ValidationError {
  constructor(
    fieldName: string,
    expectedFormat?: string,
    context?: ErrorContext
  ) {
    const message = expectedFormat 
      ? `The field '${fieldName}' has invalid format. Expected: ${expectedFormat}`
      : `The field '${fieldName}' has invalid format`
    super(message, 400, context)
  }
}

/**
 * Error thrown when field value is out of range
 */
export class ValueOutOfRangeError extends ValidationError {
  constructor(
    fieldName: string,
    min?: number | string,
    max?: number | string,
    context?: ErrorContext
  ) {
    let message = `The field '${fieldName}' is out of range`
    if (min !== undefined && max !== undefined) {
      message += `. Expected range: ${min} - ${max}`
    } else if (min !== undefined) {
      message += `. Minimum value: ${min}`
    } else if (max !== undefined) {
      message += `. Maximum value: ${max}`
    }
    super(message, 400, context)
  }
}

export default ValidationError
