import AppError, { type ErrorContext } from './AppError'

/**
 * Base class for all configuration-related errors
 */
export class ConfigurationError extends AppError {
  constructor(
    message: string = 'Configuration error',
    statusCode: number = 500,
    context?: ErrorContext
  ) {
    super(message, statusCode, context)
  }
}

/**
 * Error thrown when environment variable is missing or invalid
 */
export class EnvironmentError extends ConfigurationError {
  constructor(
    variableName: string,
    context?: ErrorContext
  ) {
    super(`Required environment variable ${variableName} is not set`, 500, context)
  }
}

/**
 * Error thrown when application setup fails
 */
export class SetupError extends ConfigurationError {
  constructor(
    message: string = 'Application setup failed',
    context?: ErrorContext
  ) {
    super(message, 500, context)
  }
}

/**
 * Error thrown when configuration file is invalid
 */
export class InvalidConfigurationError extends ConfigurationError {
  constructor(
    configName: string,
    context?: ErrorContext
  ) {
    super(`Invalid configuration: ${configName}`, 500, context)
  }
}

export default ConfigurationError
