import { type Request, type Response, type NextFunction } from 'express'
import jwt, { type JwtPayload } from 'jsonwebtoken'
import { env } from '../utils/env'
import { type IUserData, Role } from '../models/User'
import {
  TokenMissingError,
  TokenInvalidError,
  TokenExpiredError,
  PermissionDeniedError,
  DatabaseQueryError
} from '../common/errors'
import { transformError } from '../common/errors/errorUtils'

interface IRequest extends Request {
  user?: IUserData | JwtPayload
}

export const authorize = (permissions: string[]) => {
  return async (req: IRequest, res: Response, next: NextFunction) => {
    const user: IUserData | JwtPayload | undefined = req.user

    try {
      if (user !== undefined) {
        const userRoles = await Role.find({
          _id: { $in: (user.roles as any[]) || [] }
        })

        const hasPermission: boolean = permissions.every((permission) => {
          return userRoles.some((role) => role.name === permission)
        })

        if (!hasPermission) {
          throw new PermissionDeniedError('Insufficient permissions')
        }

        next()
      } else {
        throw new PermissionDeniedError('User not authenticated')
      }
    } catch (error: unknown) {
      if (error instanceof PermissionDeniedError) {
        next(error)
      } else {
        // Transform database or other errors
        const dbError = new DatabaseQueryError(
          'Failed to check user permissions',
          { cause: error }
        )
        next(dbError)
      }
    }
  }
}

export const isAuthenticated = (
  req: IRequest,
  res: Response,
  next: NextFunction
): void => {
  try {
    const token: string | undefined = req.headers.authorization?.split(' ')[1]

    if (!token) {
      throw new TokenMissingError('Authentication token is required')
    }

    const decoded = jwt.verify(token, env('JWT_SECRET_KEY'))

    req.user = decoded as IUserData
    next()
  } catch (error: unknown) {
    // Handle JWT-specific errors
    if (error instanceof jwt.JsonWebTokenError) {
      next(
        new TokenInvalidError('Invalid authentication token', { cause: error })
      )
    } else if (error instanceof jwt.TokenExpiredError) {
      next(
        new TokenExpiredError('Authentication token has expired', {
          cause: error
        })
      )
    } else if (error instanceof jwt.NotBeforeError) {
      next(new TokenInvalidError('Token not active yet', { cause: error }))
    } else if (error instanceof TokenMissingError) {
      next(error)
    } else {
      // Transform any other error
      next(transformError(error, 'Authentication failed'))
    }
  }
}
