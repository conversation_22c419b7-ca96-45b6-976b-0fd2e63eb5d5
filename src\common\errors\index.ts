// Base error class
export { default as AppError, type ErrorContext } from './AppError'

// Database errors
export {
  DatabaseError,
  DatabaseConnectionError,
  DatabaseQueryError,
  DatabaseValidationError,
  DatabaseDuplicateError,
  DatabaseTransactionError
} from './DatabaseError'

// Authentication errors
export {
  AuthenticationError,
  InvalidCredentialsError,
  TokenExpiredError,
  TokenInvalidError,
  TokenMissingError,
  AccountDisabledError
} from './AuthenticationError'

// Authorization errors
export {
  AuthorizationError,
  PermissionDeniedError,
  RoleRequiredError,
  ResourceAccessDeniedError,
  OperationForbiddenError
} from './AuthorizationError'

// Validation errors
export {
  ValidationError,
  InputValidationError,
  SchemaValidationError,
  RequiredFieldError,
  InvalidFormatError,
  ValueOutOfRangeError
} from './ValidationError'

// Resource errors
export {
  ResourceError,
  ResourceNotFoundError,
  ResourceConflictError,
  ResourceStateError
} from './ResourceError'

// Configuration errors
export {
  ConfigurationError,
  EnvironmentError,
  SetupError,
  InvalidConfigurationError
} from './ConfigurationError'

// Legacy exports for backward compatibility
export { default as InternalServerError } from './InternalServerError'
export { default as NotFoundError } from './NotFoundError'

// Default export for the most commonly used error
export { default } from './ResourceError'
