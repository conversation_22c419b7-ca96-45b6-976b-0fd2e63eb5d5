/// <reference path="../../../typings/custom.d.ts" />

import { type Request, type Response, type NextFunction } from 'express'
import User, {
  Password,
  Role,
  type IUser,
  type IUserRole,
  type IPassword
} from '../../models/User'
import {
  InvalidCredentialsError,
  ResourceConflictError,
  RequiredFieldError,
  DatabaseQueryError
} from '../../common/errors'
import { transformError } from '../../common/errors/errorUtils'

export const register = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<any> => {
  try {
    const { firstName, lastName, email, password } = req.body

    /**
     * This guard is necessary to prevent saving of the
     * password while the user is not saved due to a missing required field.
     */
    if (firstName === undefined || firstName === null) {
      res.response({
        errors: {
          message: ["The field 'firstName' is required"]
        }
      })
      return
    }

    const existingUser: IUser | null = await User.findOne({ email })
    if (existingUser != null) {
      /**
       * TODO: This is for security reasons to preserve our user's data
       * Need to have a better approach for this
       */
      res.response(
        { errors: { message: ["The field 'email' is invalid"] } },
        400
      )
      return
    }

    const newUser: IUser = new User({
      firstName,
      lastName,
      email,
      password
    })

    const userPassword: IPassword = new Password({
      hash: password,
      user: newUser._id
    })

    // Assign the default user role upon registering
    const defaultRole: IUserRole | null = await Role.findOne({
      name: 'user'
    })
    if (defaultRole == null) {
      // Create the default role if it does not exist
      const userRole = new Role({
        name: 'user',
        permissions: ['read']
      })

      newUser.roles.push(userRole._id)
      await userRole.save()
    } else {
      newUser.roles.push(defaultRole._id)
    }

    await newUser.save()
    await userPassword.save()

    const token: string = newUser.generateAuthToken()

    res.response({
      user: {
        name: `${newUser.firstName} ${newUser.lastName}`,
        firstName: newUser.firstName,
        lastName: newUser.lastName,
        email: newUser.email
      },
      token
    })
  } catch (error: unknown) {
    // Transform unknown error to typed error
    const typedError = transformError(error, 'Registration failed')
    next(typedError)
  }
}

export const login = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<any> => {
  try {
    const { email, password } = req.body

    const user: IUser | null = await User.findOne({ email })
    if (user == null) {
      res.unauthorized('Invalid credentials')
      return
    }

    const hash = await Password.findOne({ user: user._id })

    if (hash == null) {
      res.unauthorized('Invalid credentials')
      return
    }

    const isVerified = await hash.checkPassword(password)

    if (!isVerified) {
      res.unauthorized('Invalid credentials')
      return
    }

    const token: string = user.generateAuthToken()

    res.response({
      token
    })
  } catch (error: unknown) {
    // Transform unknown error to typed error
    const typedError = transformError(error, 'Login failed')
    next(typedError)
  }
}

export const logout = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<any> => {
  try {
    // Logout logic

    res.success('User successfully logged out')
  } catch (error: unknown) {
    // Transform unknown error to typed error
    const typedError = transformError(error, 'Logout failed')
    next(typedError)
  }
}
