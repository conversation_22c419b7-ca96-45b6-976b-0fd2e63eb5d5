{
  "compilerOptions": {
    "target": "es2016",
    "module": "commonjs",
    "outDir": "./dist",
    "typeRoots": ["./typings", "./node_modules/@types"],
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "skipLibCheck": true,
    // Enhanced error handling configuration
    "useUnknownInCatchVariables": true,
    "exactOptionalPropertyTypes": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true
  },
  "ts-node": {
    "files": true
  },
  "include": ["./src/**/*.ts", "./typings/**/*.ts", "./typings/**/*.d.ts"],
  "exclude": ["node_modules", "dist", "coverage"]
}
