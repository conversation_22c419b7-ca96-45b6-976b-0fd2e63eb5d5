import {
  AppError,
  DatabaseError,
  DatabaseConnectionError,
  DatabaseQueryError,
  DatabaseValidationError,
  DatabaseDuplicateError,
  AuthenticationError,
  InvalidCredentialsError,
  TokenExpiredError,
  TokenInvalidError,
  TokenMissingError,
  AuthorizationError,
  PermissionDeniedError,
  RoleRequiredError,
  ValidationError,
  InputValidationError,
  RequiredFieldError,
  InvalidFormatError,
  ResourceError,
  ResourceNotFoundError,
  ResourceConflictError,
  ConfigurationError,
  EnvironmentError,
  SetupError
} from '../../common/errors'

describe('Error Types', () => {
  describe('AppError (Base Class)', () => {
    it('should create an AppError with default values', () => {
      const error = new AppError()
      
      expect(error).toBeInstanceOf(Error)
      expect(error).toBeInstanceOf(AppError)
      expect(error.name).toBe('AppError')
      expect(error.statusCode).toBe(400)
      expect(error.message).toBe('')
    })

    it('should create an AppError with custom message and status code', () => {
      const error = new AppError('Custom error message', 500)
      
      expect(error.message).toBe('Custom error message')
      expect(error.statusCode).toBe(500)
    })

    it('should create an AppError with context', () => {
      const context = { cause: new Error('Original error'), details: { userId: '123' } }
      const error = new AppError('Error with context', 400, context)
      
      expect(error.context).toEqual(context)
    })

    it('should handle array messages', () => {
      const messages = ['Error 1', 'Error 2', 'Error 3']
      const error = new AppError(messages)
      
      expect(error.message).toBe('Error 1, Error 2, Error 3')
    })

    it('should return proper JSON representation', () => {
      const error = new AppError('Test error', 422)
      const json = error.json()
      
      expect(json).toEqual({
        name: 'AppError',
        statusCode: 422,
        message: 'Test error'
      })
    })
  })

  describe('Database Errors', () => {
    it('should create DatabaseConnectionError with correct defaults', () => {
      const error = new DatabaseConnectionError()
      
      expect(error).toBeInstanceOf(DatabaseError)
      expect(error).toBeInstanceOf(AppError)
      expect(error.statusCode).toBe(503)
      expect(error.message).toBe('Failed to connect to database')
    })

    it('should create DatabaseQueryError with correct defaults', () => {
      const error = new DatabaseQueryError()
      
      expect(error).toBeInstanceOf(DatabaseError)
      expect(error.statusCode).toBe(500)
      expect(error.message).toBe('Database query failed')
    })

    it('should create DatabaseValidationError with correct defaults', () => {
      const error = new DatabaseValidationError()
      
      expect(error).toBeInstanceOf(DatabaseError)
      expect(error.statusCode).toBe(400)
      expect(error.message).toBe('Database validation failed')
    })

    it('should create DatabaseDuplicateError with correct defaults', () => {
      const error = new DatabaseDuplicateError()
      
      expect(error).toBeInstanceOf(DatabaseError)
      expect(error.statusCode).toBe(409)
      expect(error.message).toBe('Duplicate entry found')
    })
  })

  describe('Authentication Errors', () => {
    it('should create InvalidCredentialsError with correct defaults', () => {
      const error = new InvalidCredentialsError()
      
      expect(error).toBeInstanceOf(AuthenticationError)
      expect(error).toBeInstanceOf(AppError)
      expect(error.statusCode).toBe(401)
      expect(error.message).toBe('Invalid credentials provided')
    })

    it('should create TokenExpiredError with correct defaults', () => {
      const error = new TokenExpiredError()
      
      expect(error).toBeInstanceOf(AuthenticationError)
      expect(error.statusCode).toBe(401)
      expect(error.message).toBe('Authentication token has expired')
    })

    it('should create TokenInvalidError with correct defaults', () => {
      const error = new TokenInvalidError()
      
      expect(error).toBeInstanceOf(AuthenticationError)
      expect(error.statusCode).toBe(401)
      expect(error.message).toBe('Invalid authentication token')
    })

    it('should create TokenMissingError with correct defaults', () => {
      const error = new TokenMissingError()
      
      expect(error).toBeInstanceOf(AuthenticationError)
      expect(error.statusCode).toBe(401)
      expect(error.message).toBe('Authentication token is required')
    })
  })

  describe('Authorization Errors', () => {
    it('should create PermissionDeniedError with correct defaults', () => {
      const error = new PermissionDeniedError()
      
      expect(error).toBeInstanceOf(AuthorizationError)
      expect(error).toBeInstanceOf(AppError)
      expect(error.statusCode).toBe(403)
      expect(error.message).toBe('Permission denied')
    })

    it('should create RoleRequiredError with correct defaults', () => {
      const error = new RoleRequiredError()
      
      expect(error).toBeInstanceOf(AuthorizationError)
      expect(error.statusCode).toBe(403)
      expect(error.message).toBe('Required role not found')
    })
  })

  describe('Validation Errors', () => {
    it('should create RequiredFieldError with field name', () => {
      const error = new RequiredFieldError('email')
      
      expect(error).toBeInstanceOf(ValidationError)
      expect(error).toBeInstanceOf(AppError)
      expect(error.statusCode).toBe(400)
      expect(error.message).toBe("The field 'email' is required")
    })

    it('should create InvalidFormatError with field name and format', () => {
      const error = new InvalidFormatError('email', 'valid email address')
      
      expect(error).toBeInstanceOf(ValidationError)
      expect(error.statusCode).toBe(400)
      expect(error.message).toBe("The field 'email' has invalid format. Expected: valid email address")
    })

    it('should create InvalidFormatError without expected format', () => {
      const error = new InvalidFormatError('phone')
      
      expect(error.message).toBe("The field 'phone' has invalid format")
    })
  })

  describe('Resource Errors', () => {
    it('should create ResourceNotFoundError with resource name', () => {
      const error = new ResourceNotFoundError('User', '123')
      
      expect(error).toBeInstanceOf(ResourceError)
      expect(error).toBeInstanceOf(AppError)
      expect(error.statusCode).toBe(404)
      expect(error.message).toBe('The resource User was not found')
      expect(error.context?.details).toEqual({ resource: 'User', query: '123' })
    })

    it('should create ResourceConflictError with resource name', () => {
      const error = new ResourceConflictError('User')
      
      expect(error).toBeInstanceOf(ResourceError)
      expect(error.statusCode).toBe(409)
      expect(error.message).toBe('The resource User already exists')
    })
  })

  describe('Configuration Errors', () => {
    it('should create EnvironmentError with variable name', () => {
      const error = new EnvironmentError('DATABASE_URL')
      
      expect(error).toBeInstanceOf(ConfigurationError)
      expect(error).toBeInstanceOf(AppError)
      expect(error.statusCode).toBe(500)
      expect(error.message).toBe('Required environment variable DATABASE_URL is not set')
    })

    it('should create SetupError with custom message', () => {
      const error = new SetupError('Failed to initialize database')
      
      expect(error).toBeInstanceOf(ConfigurationError)
      expect(error.statusCode).toBe(500)
      expect(error.message).toBe('Failed to initialize database')
    })
  })
})
