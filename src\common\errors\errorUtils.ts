import {
  AppError,
  DatabaseError,
  DatabaseConnectionError,
  DatabaseQueryError,
  DatabaseValidationError,
  DatabaseDuplicateError,
  AuthenticationError,
  InvalidCredentialsError,
  TokenExpiredError,
  TokenInvalidError,
  ValidationError,
  ResourceNotFoundError,
  ConfigurationError,
  type ErrorContext
} from './index'

/**
 * Type guard to check if an error is an AppError
 */
export const isAppError = (error: unknown): error is AppError => {
  return error instanceof AppError
}

/**
 * Type guard to check if an error is a database error
 */
export const isDatabaseError = (error: unknown): error is DatabaseError => {
  return error instanceof DatabaseError
}

/**
 * Type guard to check if an error is an authentication error
 */
export const isAuthenticationError = (error: unknown): error is AuthenticationError => {
  return error instanceof AuthenticationError
}

/**
 * Type guard to check if an error is a validation error
 */
export const isValidationError = (error: unknown): error is ValidationError => {
  return error instanceof ValidationError
}

/**
 * Transform unknown error to appropriate typed error
 */
export const transformError = (error: unknown, defaultMessage?: string): AppError => {
  // If it's already an AppError, return as is
  if (isAppError(error)) {
    return error
  }

  // Handle standard Error objects
  if (error instanceof Error) {
    const context: ErrorContext = { cause: error }

    // MongoDB specific errors
    if (error.name === 'MongoError' || error.name === 'MongoServerError') {
      return new DatabaseQueryError(error.message, context)
    }

    if (error.name === 'MongoNetworkError') {
      return new DatabaseConnectionError(error.message, context)
    }

    if (error.name === 'ValidationError') {
      return new DatabaseValidationError(error.message, context)
    }

    // JWT specific errors
    if (error.name === 'JsonWebTokenError') {
      return new TokenInvalidError(error.message, context)
    }

    if (error.name === 'TokenExpiredError') {
      return new TokenExpiredError(error.message, context)
    }

    if (error.name === 'NotBeforeError') {
      return new TokenInvalidError('Token not active yet', context)
    }

    // Generic Error fallback
    return new AppError(error.message || defaultMessage || 'An error occurred', 500, context)
  }

  // Handle string errors
  if (typeof error === 'string') {
    return new AppError(error || defaultMessage || 'An error occurred')
  }

  // Handle unknown errors
  return new AppError(defaultMessage || 'An unknown error occurred', 500, { cause: error })
}

/**
 * Transform MongoDB duplicate key error
 */
export const transformMongoError = (error: any): AppError => {
  const context: ErrorContext = { cause: error }

  // Duplicate key error
  if (error.code === 11000) {
    const objectRegEx = /dup key: {(.+?)}/
    const match = error.message?.match(objectRegEx)
    
    if (match) {
      const field = match[1].replace(/\\"/g, "'").trim()
      return new DatabaseDuplicateError(`The ${field} already exists`, context)
    }
    
    return new DatabaseDuplicateError('Duplicate entry found', context)
  }

  // Cast error (invalid ObjectId)
  if (error.name === 'CastError' && error.kind === 'ObjectId') {
    return new ResourceNotFoundError(error.path, error.value, context)
  }

  // Validation error
  if (error.name === 'ValidationError') {
    const messages = Object.values(error.errors || {}).map((err: any) => {
      let msg = `The field '${err.path}' is `
      
      if (err.kind === 'regexp') {
        msg += 'invalid'
      } else {
        msg += err.kind
      }
      
      return msg
    })
    
    return new DatabaseValidationError(messages.join(', '), context)
  }

  return transformError(error)
}

/**
 * Extract error message safely
 */
export const getErrorMessage = (error: unknown): string => {
  if (isAppError(error)) {
    return error.message
  }
  
  if (error instanceof Error) {
    return error.message
  }
  
  if (typeof error === 'string') {
    return error
  }
  
  return 'An unknown error occurred'
}

/**
 * Extract error status code safely
 */
export const getErrorStatusCode = (error: unknown): number => {
  if (isAppError(error)) {
    return error.statusCode
  }
  
  return 500
}
