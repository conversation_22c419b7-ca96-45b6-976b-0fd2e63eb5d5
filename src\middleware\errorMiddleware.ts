import { type Request, type Response, type NextFunction } from 'express'

import {
  App<PERSON>rror,
  DatabaseError,
  DatabaseConnectionError,
  DatabaseQueryError,
  DatabaseValidationError,
  DatabaseDuplicateError,
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  ResourceError,
  ConfigurationError
} from '../common/errors'
import {
  transformError,
  transformMongoError,
  isAppError
} from '../common/errors/errorUtils'

const errorMiddleware = (
  err: unknown,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Transform unknown errors to typed errors
  let error: AppError

  if (isAppError(err)) {
    error = err
  } else {
    // Check if it's a MongoDB error first
    if (typeof err === 'object' && err !== null && 'code' in err) {
      error = transformMongoError(err)
    } else {
      error = transformError(err)
    }
  }

  // Handle specific error types
  if (error instanceof DatabaseConnectionError) {
    res.error('Database connection failed. Please try again later.', 503)
    return
  }

  if (error instanceof DatabaseDuplicateError) {
    res.error(error.message, 409)
    return
  }

  if (error instanceof DatabaseValidationError) {
    res.response({ errors: { message: [error.message] } }, 400)
    return
  }

  if (error instanceof DatabaseError) {
    res.error('Database operation failed. Please try again later.', 500)
    return
  }

  if (error instanceof AuthenticationError) {
    res.error(error.message, error.statusCode)
    return
  }

  if (error instanceof AuthorizationError) {
    res.error(error.message, error.statusCode)
    return
  }

  if (error instanceof ValidationError) {
    res.response({ errors: { message: [error.message] } }, error.statusCode)
    return
  }

  if (error instanceof ResourceError) {
    res.error(error.message, error.statusCode)
    return
  }

  if (error instanceof ConfigurationError) {
    res.error('Server configuration error. Please contact support.', 500)
    return
  }

  // Default error response for any other AppError
  res.error(error.message, error.statusCode)
}

export default errorMiddleware
