import { type Request, type Response, type NextFunction } from 'express'
import { transformError } from '../errors/errorUtils'

/**
 * Async handler wrapper that catches errors and passes them to error middleware
 * with proper typing instead of generic 'any'
 */
const asyncHandler =
  (fn: (req: Request, res: Response, next: NextFunction) => Promise<unknown>) =>
  async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      await fn(req, res, next)
    } catch (error: unknown) {
      // Transform unknown error to typed AppError and pass to error middleware
      const typedError = transformError(error)
      next(typedError)
    }
  }

export default asyncHandler
