interface ResponseObject {
  success?: boolean
  name?: string
  message: any
  data?: any
  statusCode: number
}

interface ErrorContext {
  cause?: unknown
  details?: Record<string, any>
  [key: string]: any
}

class AppError extends Error {
  public readonly statusCode: number
  public readonly data?: any
  public readonly context?: ErrorContext

  constructor(
    message?: string | string[],
    statusCode: number = 400,
    context?: ErrorContext
  ) {
    super(message instanceof Array ? message.join(', ') : message)

    this.name = this.constructor.name
    this.statusCode = statusCode
    this.context = context

    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, new.target.prototype)

    // Capture stack trace if available
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor)
    }
  }

  json(): ResponseObject {
    const response: ResponseObject = {
      name: this.name,
      statusCode: this.statusCode,
      message: this.message
    }

    if (this.data !== null && typeof this.data !== 'undefined') {
      response.data = this.data
    }

    return response
  }
}

export default AppError
